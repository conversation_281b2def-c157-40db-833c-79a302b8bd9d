# Gemini Play - OrbStack 環境

這是一個使用 OrbStack 容器化的 Node.js 應用程式專案。

## 🚀 快速開始

### 前置需求
- [OrbStack](https://orbstack.dev/) (已安裝)
- Node.js (可選，用於本地開發)

### 安裝與執行

1. **啟動 Docker 環境 (在 OrbStack 中)**
   ```bash
   # 啟動所有服務
   docker-compose up -d

   # 或使用 npm script
   npm run docker:up
   ```

2. **查看服務狀態**
   ```bash
   docker-compose ps
   ```

3. **查看日誌**
   ```bash
   # 查看所有服務日誌
   docker-compose logs -f

   # 查看特定服務日誌
   docker-compose logs -f app

   # 或使用 npm script
   npm run docker:logs
   ```

## 📋 可用的服務

- **應用程式**: http://localhost:3000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## 🛠️ 常用指令

### Docker Compose 指令 (在 OrbStack 中運行)
```bash
# 啟動服務
docker-compose up -d

# 停止服務
docker-compose down

# 重啟服務
docker-compose restart

# 查看服務狀態
docker-compose ps

# 查看日誌
docker-compose logs -f [service_name]

# 進入容器
docker-compose exec app sh

# 重建服務
docker-compose up --build -d
```

### NPM Scripts
```bash
# 啟動 Docker 環境
npm run docker:up

# 停止 Docker 環境
npm run docker:down

# 查看日誌
npm run docker:logs

# 進入應用程式容器
npm run docker:shell

# 重建並啟動
npm run docker:build

# 本地開發模式
npm run dev

# 生產模式
npm start
```

## 🔧 API 端點

- `GET /` - 歡迎頁面
- `GET /health` - 健康檢查 (檢查資料庫和 Redis 連接)
- `GET /api/test` - 測試 API (測試所有服務)

## 📁 專案結構

```
gemini_play/
├── docker-compose.yml  # Docker Compose 設定檔
├── package.json        # Node.js 專案設定
├── index.js            # 主應用程式
├── .env                # 環境變數
└── README.md           # 專案說明
```

## 🔧 環境變數

環境變數已設定在 `.env` 檔案中：

- `DATABASE_URL`: PostgreSQL 連接字串
- `REDIS_URL`: Redis 連接字串
- `NODE_ENV`: 環境模式
- `PORT`: 應用程式端口
- `GEMINI_API_KEY`: Gemini API 金鑰

## 🐛 除錯

### 查看容器狀態
```bash
docker-compose ps
```

### 查看特定服務日誌
```bash
docker-compose logs -f app
docker-compose logs -f db
docker-compose logs -f redis
```

### 進入容器除錯
```bash
docker-compose exec app sh
```

### 重置環境
```bash
docker-compose down
docker-compose up --build -d
```

## 💡 OrbStack 優勢

- 🚀 **快速啟動**: 比傳統 Docker 更快的容器啟動速度
- 🔧 **簡化管理**: 更直觀的容器管理介面
- 💾 **資源效率**: 更好的資源利用率
- 🌐 **網路整合**: 與 macOS 網路更好的整合
- 📱 **GUI 管理**: 提供圖形化管理介面

## 📝 注意事項

1. 確保 OrbStack 已正確安裝並運行
2. 首次啟動可能需要下載映像檔，請耐心等待
3. 所有資料會持久化在 OrbStack volumes 中
4. 可以通過 OrbStack GUI 管理容器和查看資源使用情況
