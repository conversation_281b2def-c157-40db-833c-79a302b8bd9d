# Gemini Play - Docker 環境

這是一個使用 Docker 容器化的 Node.js 應用程式專案。

## 🚀 快速開始

### 前置需求
- Docker
- Docker Compose

### 安裝與執行

1. **複製環境變數檔案**
   ```bash
   cp .env.example .env
   ```

2. **建置並啟動開發環境**
   ```bash
   # 啟動開發環境
   docker-compose -f docker-compose.dev.yml up --build

   # 或者在背景執行
   docker-compose -f docker-compose.dev.yml up -d --build
   ```

3. **啟動生產環境**
   ```bash
   docker-compose up --build
   ```

## 📋 可用的服務

### 開發環境 (docker-compose.dev.yml)
- **應用程式**: http://localhost:3000
- **PostgreSQL**: localhost:5433
- **Redis**: localhost:6380

### 生產環境 (docker-compose.yml)
- **應用程式**: http://localhost:3000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **pgAdmin**: http://localhost:8080
  - Email: <EMAIL>
  - Password: admin

## 🛠️ 常用指令

### Docker Compose 指令
```bash
# 啟動服務
docker-compose up

# 在背景啟動服務
docker-compose up -d

# 停止服務
docker-compose down

# 重新建置並啟動
docker-compose up --build

# 查看日誌
docker-compose logs

# 查看特定服務的日誌
docker-compose logs app

# 進入容器
docker-compose exec app sh
```

### 資料庫相關
```bash
# 連接到 PostgreSQL
docker-compose exec db psql -U postgres -d gemini_play

# 重置資料庫
docker-compose down -v
docker-compose up --build
```

## 📁 專案結構

```
gemini_play/
├── Dockerfile              # 生產環境 Docker 檔案
├── Dockerfile.dev          # 開發環境 Docker 檔案
├── docker-compose.yml      # 生產環境 Docker Compose
├── docker-compose.dev.yml  # 開發環境 Docker Compose
├── .dockerignore           # Docker 忽略檔案
├── .env.example            # 環境變數範例
├── init.sql                # 資料庫初始化腳本
└── README.md               # 專案說明
```

## 🔧 環境變數

複製 `.env.example` 到 `.env` 並根據需要修改設定：

- `DATABASE_URL`: PostgreSQL 連接字串
- `REDIS_URL`: Redis 連接字串
- `NODE_ENV`: 環境模式 (development/production)
- `PORT`: 應用程式端口
- `GEMINI_API_KEY`: Gemini API 金鑰

## 🐛 除錯

### 查看容器狀態
```bash
docker-compose ps
```

### 查看容器日誌
```bash
docker-compose logs [service_name]
```

### 進入容器除錯
```bash
docker-compose exec app sh
```

## 📝 注意事項

1. 首次啟動時會自動建立資料庫和表格
2. 開發環境支援熱重載
3. 生產環境包含 pgAdmin 用於資料庫管理
4. 所有資料會持久化在 Docker volumes 中
