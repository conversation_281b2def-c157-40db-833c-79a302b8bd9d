# Gemini Play - OrbStack 環境

這是一個使用 OrbStack 容器化的 Node.js 應用程式專案。

## 🚀 快速開始

### 前置需求
- [OrbStack](https://orbstack.dev/) (已安裝)
- Node.js (可選，用於本地開發)

### 安裝與執行

1. **啟動 OrbStack 環境**
   ```bash
   # 啟動所有服務
   orb up

   # 或使用 npm script
   npm run orb:up
   ```

2. **查看服務狀態**
   ```bash
   orb ps
   ```

3. **查看日誌**
   ```bash
   # 查看所有服務日誌
   orb logs

   # 查看特定服務日誌
   orb logs app

   # 或使用 npm script
   npm run orb:logs
   ```

## 📋 可用的服務

- **應用程式**: http://localhost:3000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## 🛠️ 常用指令

### OrbStack 指令
```bash
# 啟動服務
orb up

# 停止服務
orb down

# 重啟服務
orb restart

# 查看服務狀態
orb ps

# 查看日誌
orb logs [service_name]

# 進入容器
orb exec app sh

# 重建服務
orb up --build
```

### NPM Scripts
```bash
# 啟動 OrbStack 環境
npm run orb:up

# 停止 OrbStack 環境
npm run orb:down

# 查看日誌
npm run orb:logs

# 進入應用程式容器
npm run orb:shell

# 本地開發模式
npm run dev

# 生產模式
npm start
```

## 🔧 API 端點

- `GET /` - 歡迎頁面
- `GET /health` - 健康檢查 (檢查資料庫和 Redis 連接)
- `GET /api/test` - 測試 API (測試所有服務)

## 📁 專案結構

```
gemini_play/
├── orb.yml          # OrbStack 設定檔
├── package.json     # Node.js 專案設定
├── index.js         # 主應用程式
├── .env             # 環境變數
└── README.md        # 專案說明
```

## 🔧 環境變數

環境變數已設定在 `.env` 檔案中：

- `DATABASE_URL`: PostgreSQL 連接字串
- `REDIS_URL`: Redis 連接字串
- `NODE_ENV`: 環境模式
- `PORT`: 應用程式端口
- `GEMINI_API_KEY`: Gemini API 金鑰

## 🐛 除錯

### 查看容器狀態
```bash
orb ps
```

### 查看特定服務日誌
```bash
orb logs app
orb logs db
orb logs redis
```

### 進入容器除錯
```bash
orb exec app sh
```

### 重置環境
```bash
orb down
orb up --build
```

## 💡 OrbStack 優勢

- 🚀 **快速啟動**: 比傳統 Docker 更快的容器啟動速度
- 🔧 **簡化管理**: 更直觀的容器管理介面
- 💾 **資源效率**: 更好的資源利用率
- 🌐 **網路整合**: 與 macOS 網路更好的整合
- 📱 **GUI 管理**: 提供圖形化管理介面

## 📝 注意事項

1. 確保 OrbStack 已正確安裝並運行
2. 首次啟動可能需要下載映像檔，請耐心等待
3. 所有資料會持久化在 OrbStack volumes 中
4. 可以通過 OrbStack GUI 管理容器和查看資源使用情況
