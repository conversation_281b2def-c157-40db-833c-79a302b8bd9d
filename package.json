{"name": "gemini-play", "version": "1.0.0", "description": "Gemini Play 專案", "main": "index.js", "scripts": {"dev": "nodemon index.js", "start": "node index.js", "test": "jest", "lint": "eslint .", "orb:up": "orb up", "orb:down": "orb down", "orb:logs": "orb logs", "orb:shell": "orb exec app sh"}, "keywords": ["gemini", "ai", "playground"], "author": "syoyo", "license": "MIT", "dependencies": {"express": "^4.18.2", "pg": "^8.11.3", "redis": "^4.6.8", "dotenv": "^16.3.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "eslint": "^8.49.0"}}