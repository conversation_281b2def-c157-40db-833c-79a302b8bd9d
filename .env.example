# 資料庫設定
DATABASE_URL=postgresql://postgres:password@localhost:5432/gemini_play
DB_HOST=localhost
DB_PORT=5432
DB_NAME=gemini_play
DB_USER=postgres
DB_PASSWORD=password

# Redis 設定
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# 應用程式設定
NODE_ENV=development
PORT=3000
APP_SECRET=your-secret-key-here

# API 金鑰 (如果需要)
GEMINI_API_KEY=your-gemini-api-key
OPENAI_API_KEY=your-openai-api-key

# JWT 設定
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRES_IN=7d

# 其他設定
LOG_LEVEL=info
CORS_ORIGIN=http://localhost:3000
