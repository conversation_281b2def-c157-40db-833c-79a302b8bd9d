# Node.js
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 環境變數檔案
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日誌檔案
logs
*.log

# 執行時檔案
pids
*.pid
*.seed
*.pid.lock

# 覆蓋率目錄
coverage
.nyc_output

# 依賴目錄
jspm_packages/

# TypeScript 快取
*.tsbuildinfo

# 可選的 npm 快取目錄
.npm

# 可選的 eslint 快取
.eslintcache

# Microbundle 快取
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# 可選的 REPL 歷史
.node_repl_history

# 輸出的 npm 包
*.tgz

# Yarn 完整性檔案
.yarn-integrity

# parcel-bundler 快取 (https://parceljs.org/)
.cache
.parcel-cache

# Next.js 建置輸出
.next

# Nuxt.js 建置 / 生成輸出
.nuxt
dist

# Gatsby 檔案
.cache/
public

# Storybook 建置輸出
.out
.storybook-out

# 暫存資料夾
tmp/
temp/

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# IDE
.vscode
.idea
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# 測試
coverage/
.coverage

# 其他
README.md
LICENSE
*.md
