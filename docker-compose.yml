

services:
  # 主應用程式
  app:
    image: node:22-alpine
    working_dir: /app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/gemini_play
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - node_modules:/app/node_modules
    command: |
      sh -c "
        npm install
        npm run dev
      "
    depends_on:
      - db
      - redis

  # PostgreSQL 資料庫
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=gemini_play
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Redis 快取
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

# 持久化儲存
volumes:
  postgres_data:
  redis_data:
  node_modules:
