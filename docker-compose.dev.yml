version: '3.8'

services:
  # 開發環境的應用程式
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
      - "9229:9229"  # Node.js 除錯端口
    environment:
      - NODE_ENV=development
      - DATABASE_URL=**************************************/gemini_play_dev
      - REDIS_URL=redis://redis:6379
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - db
      - redis
    networks:
      - dev-network
    command: npm run dev

  # 開發用資料庫
  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=gemini_play_dev
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5433:5432"  # 使用不同端口避免衝突
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - dev-network

  # Redis
  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"  # 使用不同端口避免衝突
    volumes:
      - redis_dev_data:/data
    networks:
      - dev-network

  # 熱重載代理 (如果需要)
  hot-reload:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run watch
    networks:
      - dev-network

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  dev-network:
    driver: bridge
