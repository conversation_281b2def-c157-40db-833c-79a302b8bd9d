const express = require('express');
const cors = require('cors');
const { Pool } = require('pg');
const redis = require('redis');
require('dotenv').config();

const app = express();
const port = process.env.PORT || 3000;

// 中間件
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// PostgreSQL 連接
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

// Redis 連接
const redisClient = redis.createClient({
  url: process.env.REDIS_URL
});

// 連接到 Redis
redisClient.connect().catch(console.error);

// 基本路由
app.get('/', (req, res) => {
  res.json({
    message: 'Welcome to Gemini Play!',
    status: 'running',
    timestamp: new Date().toISOString()
  });
});

// 健康檢查
app.get('/health', async (req, res) => {
  try {
    // 檢查資料庫連接
    const dbResult = await pool.query('SELECT NOW()');
    
    // 檢查 Redis 連接
    const redisResult = await redisClient.ping();
    
    res.json({
      status: 'healthy',
      database: 'connected',
      redis: redisResult === 'PONG' ? 'connected' : 'disconnected',
      timestamp: dbResult.rows[0].now
    });
  } catch (error) {
    res.status(500).json({
      status: 'unhealthy',
      error: error.message
    });
  }
});

// API 路由範例
app.get('/api/test', async (req, res) => {
  try {
    // 測試 Redis
    await redisClient.set('test-key', 'Hello from Redis!');
    const redisValue = await redisClient.get('test-key');
    
    // 測試資料庫
    const dbResult = await pool.query('SELECT version()');
    
    res.json({
      redis: redisValue,
      database: dbResult.rows[0].version,
      message: 'All services are working!'
    });
  } catch (error) {
    res.status(500).json({
      error: error.message
    });
  }
});

// 錯誤處理中間件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Something went wrong!',
    message: err.message
  });
});

// 404 處理
app.use((req, res) => {
  res.status(404).json({
    error: 'Route not found'
  });
});

// 啟動伺服器
app.listen(port, '0.0.0.0', () => {
  console.log(`🚀 Server is running on http://localhost:${port}`);
  console.log(`📊 Health check: http://localhost:${port}/health`);
  console.log(`🧪 Test API: http://localhost:${port}/api/test`);
});

// 優雅關閉
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await pool.end();
  await redisClient.quit();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await pool.end();
  await redisClient.quit();
  process.exit(0);
});
